import ServiceCard from "@/components/ServiceCard";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	ArrowRight,
	CheckCircle,
	Clock,
	Compass,
	Flower,
	Heart,
	MapPin,
	Moon,
	Phone,
	Shield,
	Sparkles,
	Star,
	Users,
	Zap,
} from "lucide-react";
import Link from "next/link";

export default function Services() {
	const mainServices = [
		{
			title: "Soin énergétique complet",
			description:
				"Rééquilibrage global de vos énergies pour retrouver harmonie et vitalité dans tous les aspects de votre vie",
			duration: "1h30",
			price: "85",
			features: [
				"Analyse énergétique approfondie",
				"Nettoyage et harmonisation des chakras",
				"Libération des blocages énergétiques",
				"Conseils personnalisés post-séance",
				"Suivi et accompagnement",
			],
			href: "/reservation/soin-energetique",
			icon: <Sparkles className="w-8 h-8" />,
		},
		{
			title: "Coaching bien-être",
			description:
				"Accompagnement personnalisé pour développer votre potentiel et créer la vie qui vous ressemble",
			duration: "1h",
			price: "65",
			features: [
				"Définition d'objectifs clairs et réalisables",
				"Techniques de développement personnel",
				"Plan d'action personnalisé",
				"Outils pratiques au quotidien",
				"Suivi régulier et ajustements",
			],
			href: "/reservation/coaching-bien-etre",
			icon: <Heart className="w-8 h-8" />,
		},
		{
			title: "Harmonisation des chakras",
			description: "Équilibrage de vos centres énergétiques pour une circulation optimale de l'énergie vitale",
			duration: "1h",
			price: "70",
			features: [
				"Diagnostic des 7 chakras principaux",
				"Techniques de rééquilibrage spécifiques",
				"Méditation guidée personnalisée",
				"Conseils d'entretien énergétique",
				"Support audio pour la pratique",
			],
			href: "/reservation/harmonisation-chakras",
			icon: <Zap className="w-8 h-8" />,
		},
		{
			title: "Libération émotionnelle",
			description: "Techniques douces pour libérer les émotions bloquées et retrouver votre liberté intérieure",
			duration: "1h15",
			price: "75",
			features: [
				"Identification des blocages émotionnels",
				"Techniques EFT et libération énergétique",
				"Travail sur les mémoires cellulaires",
				"Intégration et ancrage des changements",
				"Exercices de maintien à domicile",
			],
			href: "/reservation/liberation-emotionnelle",
			icon: <Shield className="w-8 h-8" />,
		},
		{
			title: "Soin à distance",
			description: "Séance énergétique efficace depuis chez vous, avec la même qualité qu'en présentiel",
			duration: "1h",
			price: "60",
			features: [
				"Connexion énergétique à distance",
				"Soin complet par téléphone/visio",
				"Feedback détaillé en temps réel",
				"Enregistrement audio du soin",
				"Suivi personnalisé par email",
			],
			href: "/reservation/soin-distance",
			icon: <Compass className="w-8 h-8" />,
		},
		{
			title: "Accompagnement grossesse",
			description:
				"Soutien énergétique spécialisé pour vivre sereinement votre grossesse et préparer l'arrivée de bébé",
			duration: "1h",
			price: "70",
			features: [
				"Harmonisation mère-enfant",
				"Gestion du stress et des peurs",
				"Préparation énergétique à l'accouchement",
				"Connexion avec votre bébé",
				"Accompagnement post-natal",
			],
			href: "/reservation/accompagnement-grossesse",
			icon: <Flower className="w-8 h-8" />,
		},
	];

	const additionalServices = [
		{
			title: "Séance découverte",
			description: "Premier rendez-vous pour faire connaissance et définir vos besoins",
			duration: "30min",
			price: "Gratuit",
			icon: <Star className="w-6 h-6" />,
		},
		{
			title: "Forfait 3 séances",
			description: "Accompagnement sur 3 mois pour un travail en profondeur",
			duration: "3x1h",
			price: "180€",
			icon: <Users className="w-6 h-6" />,
		},
		{
			title: "Soin de nuit",
			description: "Séance énergétique pendant votre sommeil pour une intégration optimale",
			duration: "Nuit",
			price: "45€",
			icon: <Moon className="w-6 h-6" />,
		},
	];

	const processSteps = [
		{
			step: "1",
			title: "Premier contact",
			description: "Échange téléphonique gratuit pour comprendre vos besoins et répondre à vos questions.",
		},
		{
			step: "2",
			title: "Prise de rendez-vous",
			description: "Planification de votre séance selon vos disponibilités, en cabinet ou à distance.",
		},
		{
			step: "3",
			title: "Séance personnalisée",
			description: "Accompagnement adapté à votre situation avec techniques sur-mesure.",
		},
		{
			step: "4",
			title: "Suivi et intégration",
			description: "Conseils personnalisés et suivi pour ancrer durablement les bienfaits.",
		},
	];

	return (
		<div className="pt-20">
			{/* Hero Section */}
			<section className="py-20 bg-gradient-to-br from-cream to-sand/30">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<div className="inline-flex items-center px-6 py-3 rounded-full bg-gold/15 border-2 border-gold/30 mb-8 shadow-lg">
						<Sparkles className="w-5 h-5 text-gold mr-3" />
						<span className="text-lg font-dm-serif text-gold font-bold">Mes services</span>
					</div>

					<h1 className="font-new-york text-4xl md:text-5xl font-bold text-olive mb-6">
						Des soins adaptés à vos besoins
					</h1>

					<p className="font-doulos text-xl text-olive/80 leading-relaxed mb-8 max-w-2xl mx-auto">
						Découvrez mes différentes approches thérapeutiques pour vous accompagner vers un mieux-être
						durable et révéler votre potentiel authentique.
					</p>

					<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
						<Button
							asChild
							size="lg"
							className="bg-gold hover:bg-gold/90 text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group"
						>
							<Link href="/reservation" className="flex items-center">
								Prendre rendez-vous
								<ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
							</Link>
						</Button>

						<Button
							asChild
							variant="outline"
							size="lg"
							className="border-2 border-gold text-gold bg-cream hover:bg-gold hover:text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-lg"
						>
							<Link href="/about">Mon approche</Link>
						</Button>
					</div>
				</div>
			</section>

			{/* Main Services */}
			<section className="py-20 bg-white/80">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Services principaux
						</h2>
						<p className="font-doulos text-xl text-olive/80 max-w-2xl mx-auto">
							Chaque service est personnalisé selon votre situation et vos objectifs
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
						{mainServices.map((service, index) => (
							<ServiceCard key={index} {...service} />
						))}
					</div>
				</div>
			</section>

			{/* Additional Services */}
			<section className="py-20 bg-gradient-to-br from-cream to-sand/30">
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Formules complémentaires
						</h2>
						<p className="font-doulos text-xl text-olive/80">
							Des options flexibles pour s'adapter à tous les besoins
						</p>
					</div>

					<div className="grid md:grid-cols-3 gap-6">
						{additionalServices.map((service, index) => (
							<Card
								key={index}
								className="bg-white/80 backdrop-blur-sm border-sand/50 hover:border-gold/30 transition-all duration-300 hover:shadow-xl hover:-translate-y-2"
							>
								<CardHeader className="text-center pb-4">
									<div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-gold/10 to-gold/5 rounded-full flex items-center justify-center">
										<div className="text-gold">{service.icon}</div>
									</div>
									<CardTitle className="font-new-york text-lg text-olive">{service.title}</CardTitle>
								</CardHeader>
								<CardContent className="text-center">
									<p className="font-sans text-olive/70 text-lg mb-4 leading-relaxed">
										{service.description}
									</p>
									<div className="flex justify-between items-center py-2 px-4 bg-cream/50 rounded-lg mb-4">
										<div className="flex items-center text-olive/80">
											<Clock className="w-5 h-5 mr-2" />
											<span className="font-sans text-base">{service.duration}</span>
										</div>
										<div className="text-gold font-semibold font-doulos">{service.price}</div>
									</div>
									<Button
										asChild
										size="sm"
										variant="outline"
										className="w-full border-gold text-gold hover:bg-gold hover:text-white font-doulos rounded-full"
									>
										<Link href="/contact">Me contacter</Link>
									</Button>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Process Section */}
			<section className="py-20 bg-white/80">
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Comment se déroule un accompagnement ?
						</h2>
						<p className="font-doulos text-xl text-olive/80">
							Un processus simple et bienveillant pour votre transformation
						</p>
					</div>

					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
						{processSteps.map((step, index) => (
							<div key={index} className="text-center group">
								<div className="relative mb-6">
									<div className="w-16 h-16 mx-auto bg-gradient-to-br from-gold to-gold/80 rounded-full flex items-center justify-center text-white font-bold text-xl font-new-york group-hover:scale-110 transition-transform duration-300">
										{step.step}
									</div>
									{index < processSteps.length - 1 && (
										<div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gold/20 -translate-x-8"></div>
									)}
								</div>
								<h3 className="font-dm-serif text-xl font-semibold text-olive mb-3">{step.title}</h3>
								<p className="font-sans text-olive/70 text-base leading-relaxed">{step.description}</p>
							</div>
						))}
					</div>
				</div>
			</section>

			{/* Practical Info */}
			<section className="py-20 bg-gradient-to-br from-olive/5 to-gold/5">
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Informations pratiques
						</h2>
					</div>

					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
						<Card className="bg-white/80 backdrop-blur-sm border-sand/30">
							<CardHeader>
								<div className="w-12 h-12 bg-gold/10 rounded-full flex items-center justify-center mb-4">
									<MapPin className="w-6 h-6 text-gold" />
								</div>
								<CardTitle className="font-new-york text-lg text-olive">Modalités</CardTitle>
							</CardHeader>
							<CardContent>
								<ul className="space-y-2 font-sans text-olive/80 text-base">
									<li className="flex items-center">
										<CheckCircle className="w-4 h-4 text-gold mr-2 flex-shrink-0" />
										Séances en cabinet ou à distance
									</li>
									<li className="flex items-center">
										<CheckCircle className="w-4 h-4 text-gold mr-2 flex-shrink-0" />
										Rendez-vous du lundi au samedi
									</li>
									<li className="flex items-center">
										<CheckCircle className="w-4 h-4 text-gold mr-2 flex-shrink-0" />
										Horaires flexibles selon vos besoins
									</li>
									<li className="flex items-center">
										<CheckCircle className="w-4 h-4 text-gold mr-2 flex-shrink-0" />
										Annulation possible 24h avant
									</li>
								</ul>
							</CardContent>
						</Card>

						<Card className="bg-white/80 backdrop-blur-sm border-sand/30">
							<CardHeader>
								<div className="w-12 h-12 bg-gold/10 rounded-full flex items-center justify-center mb-4">
									<Phone className="w-6 h-6 text-gold" />
								</div>
								<CardTitle className="font-new-york text-lg text-olive">Contact</CardTitle>
							</CardHeader>
							<CardContent>
								<ul className="space-y-2 font-sans text-olive/80 text-base">
									<li className="flex items-center">
										<CheckCircle className="w-4 h-4 text-gold mr-2 flex-shrink-0" />
										Premier échange téléphonique gratuit
									</li>
									<li className="flex items-center">
										<CheckCircle className="w-4 h-4 text-gold mr-2 flex-shrink-0" />
										Réponse sous 24h maximum
									</li>
									<li className="flex items-center">
										<CheckCircle className="w-4 h-4 text-gold mr-2 flex-shrink-0" />
										Disponible par téléphone et email
									</li>
									<li className="flex items-center">
										<CheckCircle className="w-4 h-4 text-gold mr-2 flex-shrink-0" />
										Suivi personnalisé après séance
									</li>
								</ul>
							</CardContent>
						</Card>

						<Card className="bg-white/80 backdrop-blur-sm border-sand/30">
							<CardHeader>
								<div className="w-12 h-12 bg-gold/10 rounded-full flex items-center justify-center mb-4">
									<Shield className="w-6 h-6 text-gold" />
								</div>
								<CardTitle className="font-new-york text-lg text-olive">Garanties</CardTitle>
							</CardHeader>
							<CardContent>
								<ul className="space-y-2 font-sans text-olive/80 text-base">
									<li className="flex items-center">
										<CheckCircle className="w-4 h-4 text-gold mr-2 flex-shrink-0" />
										Confidentialité absolue garantie
									</li>
									<li className="flex items-center">
										<CheckCircle className="w-4 h-4 text-gold mr-2 flex-shrink-0" />
										Approche respectueuse et bienveillante
									</li>
									<li className="flex items-center">
										<CheckCircle className="w-4 h-4 text-gold mr-2 flex-shrink-0" />
										Techniques éprouvées et sécurisées
									</li>
									<li className="flex items-center">
										<CheckCircle className="w-4 h-4 text-gold mr-2 flex-shrink-0" />
										Satisfaction client prioritaire
									</li>
								</ul>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-20 bg-gradient-to-br from-gold/10 to-sand/20">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 md:p-12 shadow-xl border border-sand/30">
						<Sparkles className="w-12 h-12 text-gold mx-auto mb-6" />

						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Prêt(e) à commencer votre transformation ?
						</h2>

						<p className="font-doulos text-xl text-olive/80 mb-8 max-w-2xl mx-auto">
							Chaque parcours commence par un premier pas. Offrons-nous un échange pour découvrir ensemble
							le service qui vous correspond le mieux.
						</p>

						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Button
								asChild
								size="lg"
								className="bg-gold hover:bg-gold/90 text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group"
							>
								<Link href="/reservation" className="flex items-center">
									Prendre rendez-vous
									<ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
								</Link>
							</Button>

							<Button
								asChild
								variant="outline"
								size="lg"
								className="border-2 border-gold text-gold bg-cream hover:bg-gold hover:text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-lg"
							>
								<Link href="/testimonials">Lire les témoignages</Link>
							</Button>
						</div>

						<p className="font-sans text-base text-olive/60 mt-6">
							Premier échange téléphonique gratuit • Paiement sécurisé • Satisfaction garantie
						</p>
					</div>
				</div>
			</section>
		</div>
	);
}
