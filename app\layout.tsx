import Footer from "@/components/Footer";
import Header from "@/components/Header";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import type React from "react";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
	title: "<PERSON> - Coach en Soins Énergétiques & Bien-être",
	description:
		"Accompagnement personnalisé en soins énergétiques, coaching bien-être et développement personnel avec <PERSON>",
	keywords: "soins énergétiques, coaching bien-être, développement personnel, thérapie holistique",
	generator: "v0.dev",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
	return (
		<html lang="fr">
			<head>
				<link
					href="https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=DM+Serif+Display:ital@0;1&display=swap"
					rel="stylesheet"
				/>
				<link href="https://fonts.googleapis.com/css2?family=Early+Sunday&display=swap" rel="stylesheet" />
			</head>
			<body className={`${inter.className} bg-cream text-olive`}>
				<Header />
				<main className="min-h-screen">{children}</main>
				<Footer />
			</body>
		</html>
	);
}
