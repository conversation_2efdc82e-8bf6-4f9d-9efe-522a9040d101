"use client";

import Image from "next/image";
import { useEffect, useState } from "react";

interface CarouselImage {
	src: string;
	alt: string;
}

const heroImages: CarouselImage[] = [
	{
		src: "/laurence-gemin-portrait.png",
		alt: "<PERSON> - <PERSON> certifiée en soins énergétiques",
	},
	{
		src: "/laurence-gemin-portrait.png", // We'll use the same image for now, but this can be replaced
		alt: "<PERSON> de coaching bien-être",
	},
	{
		src: "/laurence-gemin-portrait.png", // We'll use the same image for now, but this can be replaced
		alt: "<PERSON> - Harmonisation des chakras",
	},
];

export default function HeroCarousel() {
	const [currentIndex, setCurrentIndex] = useState(0);
	const [isAutoPlaying, setIsAutoPlaying] = useState(true);

	useEffect(() => {
		if (!isAutoPlaying) return;

		const interval = setInterval(() => {
			setCurrentIndex((prevIndex) => (prevIndex === heroImages.length - 1 ? 0 : prevIndex + 1));
		}, 4000); // Change image every 4 seconds

		return () => clearInterval(interval);
	}, [isAutoPlaying]);

	return (
		<div className="relative w-full max-w-xs sm:max-w-sm lg:max-w-lg xl:max-w-xl ml-20 sm:ml-20 lg:ml-0">
			{/* Main Image Container - Clean floating cutout */}
			<div className="relative overflow-hidden">
				<div
					className="flex transition-transform duration-1000 ease-in-out"
					style={{ transform: `translateX(-${currentIndex * 100}%)` }}
				>
					{heroImages.map((image, index) => (
						<div key={index} className="w-full flex-shrink-0">
							<Image
								src={image.src}
								alt={image.alt}
								width={500}
								height={600}
								className="w-full h-auto object-contain object-bottom drop-shadow-2xl"
								priority={index === 0}
							/>
						</div>
					))}
				</div>
			</div>

			{/* Subtle glow effect behind the carousel */}
			<div className="absolute inset-0 bg-gradient-to-br from-gold/20 via-transparent to-blue/20 rounded-full blur-3xl -z-10 scale-110"></div>
		</div>
	);
}
