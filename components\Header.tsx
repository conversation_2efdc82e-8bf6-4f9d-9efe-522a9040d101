"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>u, <PERSON>, X } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

const Header = () => {
	const [isMenuOpen, setIsMenuOpen] = useState(false);
	const [isScrolled, setIsScrolled] = useState(false);
	const pathname = usePathname();

	useEffect(() => {
		const handleScroll = () => {
			setIsScrolled(window.scrollY > 50);
		};
		window.addEventListener("scroll", handleScroll);
		return () => window.removeEventListener("scroll", handleScroll);
	}, []);

	const navigation = [
		{ name: "Accueil", href: "/" },
		{ name: "À propos", href: "/about" },
		{ name: "Services", href: "/services" },
		{ name: "Témoignages", href: "/testimonials" },
		{ name: "Événements", href: "/events" },
		{ name: "Contact", href: "/contact" },
	];

	return (
		<header
			className={`fixed top-0 w-full z-50 transition-all duration-300 ${
				isScrolled ? "bg-cream/95 backdrop-blur-sm shadow-lg" : "bg-transparent"
			}`}
		>
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="flex justify-between items-center py-4">
					{/* Logo */}
					<Link href="/" className="flex items-center space-x-2 group">
						<div className="relative">
							<div className="w-12 h-12 rounded-full bg-gradient-to-br from-gold to-gold/80 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
								<Star className="w-6 h-6 text-white" />
							</div>
							<div className="absolute -inset-1 rounded-full bg-gradient-to-br from-gold/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
						</div>
						<div className="hidden sm:block">
							<h1 className="font-new-york text-xl font-bold text-gold">Laurence Gémin</h1>
							<p className="text-base text-olive font-crimson italic">Coach Bien-être</p>
						</div>
					</Link>

					{/* Desktop Navigation */}
					<nav className="hidden lg:flex items-center space-x-8">
						{navigation.map((item) => (
							<Link
								key={item.name}
								href={item.href}
								className={`font-doulos text-base font-medium transition-colors duration-200 hover:text-gold relative ${
									pathname === item.href ? "text-gold" : "text-olive"
								}`}
							>
								{item.name}
								{pathname === item.href && (
									<div className="absolute -bottom-1 left-0 w-full h-0.5 bg-gold rounded-full"></div>
								)}
							</Link>
						))}
					</nav>

					{/* CTA Button */}
					<div className="hidden lg:block">
						<Button
							asChild
							className="bg-gold hover:bg-gold/90 text-white font-doulos font-medium px-6 py-2 rounded-full transition-all duration-300 hover:shadow-lg hover:scale-105"
						>
							<Link href="/reservation">Prendre rendez-vous</Link>
						</Button>
					</div>

					{/* Mobile menu button */}
					<button
						onClick={() => setIsMenuOpen(!isMenuOpen)}
						className="lg:hidden p-3 rounded-md text-olive hover:text-gold transition-colors duration-200 min-h-[48px] min-w-[48px] flex items-center justify-center"
					>
						{isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
					</button>
				</div>

				{/* Mobile Navigation */}
				{isMenuOpen && (
					<div className="lg:hidden py-4 border-t border-sand bg-cream/95 backdrop-blur-sm">
						<nav className="flex flex-col space-y-4">
							{navigation.map((item) => (
								<Link
									key={item.name}
									href={item.href}
									onClick={() => setIsMenuOpen(false)}
									className={`font-doulos text-lg font-medium transition-colors duration-200 hover:text-gold px-4 py-3 rounded-lg hover:bg-gold/10 ${
										pathname === item.href ? "text-gold bg-gold/10" : "text-olive"
									}`}
								>
									{item.name}
								</Link>
							))}
							<Button
								asChild
								className="bg-gold hover:bg-gold/90 text-white font-doulos font-medium mt-4 rounded-full"
							>
								<Link href="/reservation" onClick={() => setIsMenuOpen(false)}>
									Prendre rendez-vous
								</Link>
							</Button>
						</nav>
					</div>
				)}
			</div>
		</header>
	);
};

export default Header;
