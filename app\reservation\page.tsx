"use client";

import ProgressBar from "@/components/reservation/ProgressBar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Clock, Compass, Euro, Heart, Shield, Sparkles, Users, Zap } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

// Mock data for services
const services = [
	{
		id: "soin-energetique",
		name: "Soin énergétique complet",
		description: "Rééquilibrage global de vos énergies pour retrouver harmonie et vitalité",
		duration: "1h30",
		price: 85,
		icon: <Sparkles className="w-8 h-8" />,
		type: "soin",
	},
	{
		id: "coaching-bien-etre",
		name: "Coaching bien-être",
		description: "Accompagnement personnalisé pour développer votre potentiel",
		duration: "1h",
		price: 65,
		icon: <Heart className="w-8 h-8" />,
		type: "coaching",
	},
	{
		id: "harmonisation-chakras",
		name: "Harmonisation des chakras",
		description: "Équilibrage de vos centres énergétiques pour une circulation optimale",
		duration: "1h",
		price: 70,
		icon: <Zap className="w-8 h-8" />,
		type: "soin",
	},
	{
		id: "liberation-emotionnelle",
		name: "Libération émotionnelle",
		description: "Techniques douces pour libérer les émotions bloquées",
		duration: "1h15",
		price: 75,
		icon: <Shield className="w-8 h-8" />,
		type: "soin",
	},
	{
		id: "soin-distance",
		name: "Soin à distance",
		description: "Séance énergétique efficace depuis chez vous",
		duration: "1h",
		price: 60,
		icon: <Compass className="w-8 h-8" />,
		type: "soin",
	},
	{
		id: "accompagnement-grossesse",
		name: "Accompagnement grossesse",
		description: "Soutien énergétique spécialisé pour vivre sereinement votre grossesse",
		duration: "1h",
		price: 70,
		icon: <Sparkles className="w-8 h-8" />,
		type: "soin",
	},
];

export default function ReservationPage() {
	const [selectedService, setSelectedService] = useState<string | null>(null);

	return (
		<div className="pt-20 min-h-screen bg-gradient-to-br from-cream to-sand/30">
			{/* Progress Bar */}
			<ProgressBar
				currentStep={1}
				totalSteps={4}
				steps={["Choix du service", "Informations", "Confirmation", "Paiement"]}
			/>

			<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
				{/* Header */}
				<div className="text-center mb-16">
					<div className="inline-flex items-center px-4 py-2 rounded-full bg-gold/10 border border-gold/20 mb-6">
						<Users className="w-4 h-4 text-gold mr-2" />
						<span className="text-sm font-crimson text-gold font-medium">Étape 1/4</span>
					</div>

					<h1 className="font-new-york text-4xl md:text-5xl font-bold text-olive mb-6">
						Choisissez votre service
					</h1>

					<p className="font-doulos text-lg text-olive/80 leading-relaxed mb-8 max-w-2xl mx-auto">
						Sélectionnez le service qui correspond le mieux à vos besoins. Chaque accompagnement est
						personnalisé selon votre situation.
					</p>
				</div>

				{/* Services Grid */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
					{services.map((service) => (
						<Card
							key={service.id}
							className={`group bg-white/80 backdrop-blur-sm border-sand/50 hover:border-gold/30 transition-all duration-300 hover:shadow-xl hover:-translate-y-2 cursor-pointer ${
								selectedService === service.id ? "ring-2 ring-gold/50 border-gold" : ""
							}`}
							onClick={() => setSelectedService(service.id)}
						>
							<CardHeader className="text-center pb-4">
								<div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gold/10 to-gold/5 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
									<div className="text-gold">{service.icon}</div>
								</div>
								<CardTitle className="font-new-york text-xl font-bold text-olive mb-2">
									{service.name}
								</CardTitle>
								<p className="font-doulos text-olive/70 text-sm leading-relaxed">
									{service.description}
								</p>
							</CardHeader>

							<CardContent className="space-y-4">
								{/* Duration & Price */}
								<div className="flex justify-between items-center py-3 px-4 bg-cream/50 rounded-lg">
									<div className="flex items-center text-olive/80">
										<Clock className="w-4 h-4 mr-2" />
										<span className="font-doulos text-sm">{service.duration}</span>
									</div>
									<div className="flex items-center text-gold font-semibold">
										<Euro className="w-4 h-4 mr-1" />
										<span className="font-doulos">{service.price}€</span>
									</div>
								</div>

								{/* CTA Button */}
								<Button
									asChild
									className="w-full bg-gold hover:bg-gold/90 text-white font-doulos font-medium rounded-full transition-all duration-300 group-hover:shadow-lg"
								>
									<Link
										href={`/reservation/${service.id}`}
										className="flex items-center justify-center"
									>
										Continuer avec ce service
										<ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
									</Link>
								</Button>
							</CardContent>
						</Card>
					))}
				</div>

				{/* Help Section */}
				<div className="mt-16 text-center">
					<Card className="bg-white/80 backdrop-blur-sm border-sand/30 max-w-2xl mx-auto">
						<CardContent className="p-8">
							<Heart className="w-12 h-12 text-gold mx-auto mb-4" />
							<h3 className="font-new-york text-xl font-semibold text-olive mb-4">
								Besoin d'aide pour choisir ?
							</h3>
							<p className="font-doulos text-olive/80 mb-6">
								Si vous hésitez entre plusieurs services, je vous propose un entretien découverte
								gratuit de 15-20 minutes pour définir ensemble vos besoins.
							</p>
							<Button
								asChild
								variant="outline"
								className="border-2 border-gold text-gold hover:bg-gold hover:text-white font-doulos font-medium px-6 py-2 rounded-full"
							>
								<Link href="/reservation/first-encounter">Entretien découverte gratuit</Link>
							</Button>
						</CardContent>
					</Card>
				</div>
			</div>
		</div>
	);
}
