"use client"

import type React from "react"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CreditCard, Lock, ArrowLeft, Shield, CheckCircle } from "lucide-react"
import Link from "next/link"
import ProgressBar from "@/components/reservation/ProgressBar"

export default function PaiementPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [formData, setFormData] = useState({
    cardName: "",
    cardNumber: "",
    expiryDate: "",
    cvv: "",
  })
  const [isProcessing, setIsProcessing] = useState(false)

  // Mock reservation data from URL params
  const reservationData = {
    service: searchParams.get("service") || "Soin énergétique complet",
    price: searchParams.get("price") || "85",
    date: searchParams.get("date") || "2024-03-15",
    time: searchParams.get("time") || "14:00",
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const formatCardNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, "")
    // Add spaces every 4 digits
    return digits.replace(/(\d{4})(?=\d)/g, "$1 ")
  }

  const formatExpiryDate = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, "")
    // Add slash after 2 digits
    if (digits.length >= 2) {
      return digits.substring(0, 2) + "/" + digits.substring(2, 4)
    }
    return digits
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsProcessing(true)

    // TODO: connect backend here
    // Simulate payment processing
    setTimeout(() => {
      router.push("/paiement/succes")
    }, 3000)
  }

  const isFormValid = () => {
    return (
      formData.cardName &&
      formData.cardNumber.replace(/\s/g, "").length === 16 &&
      formData.expiryDate.length === 5 &&
      formData.cvv.length === 3
    )
  }

  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-cream to-sand/30">
      {/* Progress Bar */}
      <ProgressBar
        currentStep={4}
        totalSteps={4}
        steps={["Choix du service", "Informations", "Confirmation", "Paiement"]}
      />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* Back Button */}
        <div className="mb-8">
          <Button asChild variant="ghost" className="text-olive hover:text-gold">
            <Link href="/reservation/confirmation" className="flex items-center">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Retour à la confirmation
            </Link>
          </Button>
        </div>

        {/* Step Indicator */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gold/10 border border-gold/20">
            <span className="text-sm font-crimson text-gold font-medium">Étape 4/4 - Paiement sécurisé</span>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Order Summary */}
          <div>
            <Card className="bg-white/80 backdrop-blur-sm border-sand/30 mb-6">
              <CardHeader>
                <CardTitle className="font-new-york text-xl text-olive">Récapitulatif de commande</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-doulos font-medium text-olive">{reservationData.service}</h3>
                    <p className="font-doulos text-olive/70 text-sm">
                      {new Date(reservationData.date).toLocaleDateString("fr-FR", {
                        weekday: "long",
                        day: "numeric",
                        month: "long",
                      })}{" "}
                      à {reservationData.time}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-new-york text-xl font-bold text-gold">{reservationData.price}€</p>
                  </div>
                </div>

                <div className="border-t border-sand/30 pt-4">
                  <div className="flex justify-between items-center">
                    <span className="font-doulos font-medium text-olive">Total</span>
                    <span className="font-new-york text-2xl font-bold text-gold">{reservationData.price}€</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Security Info */}
            <Card className="bg-gradient-to-br from-gold/5 to-cream/30 border-sand/30">
              <CardContent className="p-6">
                <div className="flex items-start space-x-3">
                  <Shield className="w-6 h-6 text-gold mt-1" />
                  <div>
                    <h4 className="font-doulos font-medium text-olive mb-2">Paiement sécurisé</h4>
                    <div className="space-y-1 text-sm font-doulos text-olive/80">
                      <p>• Connexion SSL cryptée</p>
                      <p>• Données bancaires protégées</p>
                      <p>• Aucune information stockée</p>
                      <p>• Remboursement possible sous 48h</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Form */}
          <div>
            <Card className="bg-white/80 backdrop-blur-sm border-sand/30 shadow-lg">
              <CardHeader>
                <CardTitle className="font-new-york text-2xl text-olive text-center flex items-center justify-center">
                  <Lock className="w-5 h-5 mr-2" />
                  Paiement sécurisé
                </CardTitle>
                <p className="font-doulos text-olive/70 text-center">Finalisez votre réservation en toute sécurité</p>
              </CardHeader>

              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Card Name */}
                  <div className="space-y-2">
                    <Label htmlFor="cardName" className="font-doulos text-olive">
                      Nom sur la carte *
                    </Label>
                    <Input
                      id="cardName"
                      value={formData.cardName}
                      onChange={(e) => handleInputChange("cardName", e.target.value)}
                      required
                      className="border-sand focus:border-gold"
                      placeholder="Nom complet"
                    />
                  </div>

                  {/* Card Number */}
                  <div className="space-y-2">
                    <Label htmlFor="cardNumber" className="font-doulos text-olive">
                      Numéro de carte *
                    </Label>
                    <div className="relative">
                      <Input
                        id="cardNumber"
                        value={formData.cardNumber}
                        onChange={(e) => {
                          const formatted = formatCardNumber(e.target.value)
                          if (formatted.replace(/\s/g, "").length <= 16) {
                            handleInputChange("cardNumber", formatted)
                          }
                        }}
                        required
                        className="border-sand focus:border-gold pl-10"
                        placeholder="1234 5678 9012 3456"
                      />
                      <CreditCard className="w-5 h-5 text-gold absolute left-3 top-1/2 transform -translate-y-1/2" />
                    </div>
                  </div>

                  {/* Expiry and CVV */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="expiryDate" className="font-doulos text-olive">
                        Date d'expiration *
                      </Label>
                      <Input
                        id="expiryDate"
                        value={formData.expiryDate}
                        onChange={(e) => {
                          const formatted = formatExpiryDate(e.target.value)
                          if (formatted.replace(/\D/g, "").length <= 4) {
                            handleInputChange("expiryDate", formatted)
                          }
                        }}
                        required
                        className="border-sand focus:border-gold"
                        placeholder="MM/AA"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="cvv" className="font-doulos text-olive">
                        CVV *
                      </Label>
                      <Input
                        id="cvv"
                        value={formData.cvv}
                        onChange={(e) => {
                          const value = e.target.value.replace(/\D/g, "")
                          if (value.length <= 3) {
                            handleInputChange("cvv", value)
                          }
                        }}
                        required
                        className="border-sand focus:border-gold"
                        placeholder="123"
                        type="password"
                      />
                    </div>
                  </div>

                  {/* Terms */}
                  <div className="bg-gradient-to-br from-gold/5 to-cream/30 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-gold mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="font-doulos text-olive/80 text-sm">
                          En procédant au paiement, vous acceptez nos{" "}
                          <Link href="/conditions" className="text-gold hover:underline">
                            conditions générales
                          </Link>{" "}
                          et notre{" "}
                          <Link href="/confidentialite" className="text-gold hover:underline">
                            politique de confidentialité
                          </Link>
                          .
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    disabled={!isFormValid() || isProcessing}
                    className="w-full bg-gold hover:bg-gold/90 text-white font-doulos font-medium py-3 rounded-full transition-all duration-300 hover:shadow-lg disabled:opacity-50"
                  >
                    {isProcessing ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Traitement en cours...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <Lock className="w-4 h-4 mr-2" />
                        Payer maintenant {reservationData.price}€
                      </div>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
