import TestimonialSlider from "@/components/TestimonialSlider"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Star, Heart, Users, ArrowRight } from "lucide-react"
import FAQSection from "@/components/FAQSection"

export default function Testimonials() {
  const stats = [
    {
      number: "500+",
      label: "Accompagnements réalisés",
      icon: <Users className="w-8 h-8 text-gold" />,
    },
    {
      number: "98%",
      label: "Clients satisfaits",
      icon: <Star className="w-8 h-8 text-gold" />,
    },
    {
      number: "4.9/5",
      label: "Note moyenne",
      icon: <Heart className="w-8 h-8 text-gold" />,
    },
  ]

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-cream to-sand/30">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gold/10 border border-gold/20 mb-6">
            <Users className="w-4 h-4 text-gold mr-2" />
            <span className="text-sm font-crimson text-gold font-medium">Témoignages</span>
          </div>

          <h1 className="font-new-york text-4xl md:text-5xl font-bold text-olive mb-6">
            Ils ont retrouvé leur équilibre
          </h1>

          <p className="font-doulos text-lg text-olive/80 leading-relaxed mb-8 max-w-2xl mx-auto">
            Découvrez les témoignages authentiques de personnes qui ont fait confiance à mon accompagnement pour
            transformer leur vie et révéler leur potentiel.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              asChild
              size="lg"
              className="bg-gold hover:bg-gold/90 text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group"
            >
              <Link href="/reservation" className="flex items-center">
                Prendre rendez-vous
                <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </Button>

            <Button
              asChild
              variant="outline"
              size="lg"
              className="border-2 border-gold text-gold hover:bg-gold hover:text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-lg"
            >
              <Link href="/services">Découvrir les services</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white/80">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8 text-center">
            {stats.map((stat, index) => (
              <div key={index} className="group">
                <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-gold/10 to-gold/5 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <div className="font-new-york text-3xl font-bold text-olive mb-2">{stat.number}</div>
                <p className="font-doulos text-olive/70">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonial Slider */}
      <section className="py-20 bg-gradient-to-br from-olive/5 to-gold/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">Témoignages</h2>
            <p className="font-doulos text-lg text-olive/80">Des transformations authentiques et durables</p>
          </div>

          <TestimonialSlider />
        </div>
      </section>

      {/* Separator */}
      <section className="py-12 bg-white/80">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center">
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gold/30 to-transparent"></div>
            <div className="px-6">
              <div className="w-3 h-3 bg-gold/20 rounded-full"></div>
            </div>
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gold/30 to-transparent"></div>
          </div>
        </div>
      </section>

      {/* Video Testimonials Placeholder */}
      <section className="py-20 bg-gradient-to-br from-cream to-sand/30">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">Témoignages vidéo</h2>
            <p className="font-doulos text-lg text-olive/80">Écoutez directement leurs expériences</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((index) => (
              <Card
                key={index}
                className="bg-white/80 backdrop-blur-sm border-sand/30 hover:shadow-lg transition-all duration-300"
              >
                <CardContent className="p-0">
                  <div className="aspect-video bg-gradient-to-br from-sand/20 to-gold/10 rounded-t-lg flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gold/20 rounded-full flex items-center justify-center mx-auto mb-3">
                        <div className="w-0 h-0 border-l-[8px] border-l-gold border-y-[6px] border-y-transparent ml-1"></div>
                      </div>
                      <p className="font-doulos text-olive/60 text-sm">Témoignage vidéo #{index}</p>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-new-york text-sm font-semibold text-olive mb-1">Témoignage de Marie</h3>
                    <p className="font-doulos text-olive/70 text-xs">Soin énergétique complet • 3 min</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQSection
        faqs={[
          {
            question: "Comment se déroule une première séance ?",
            answer:
              "La première séance commence toujours par un échange pour comprendre vos besoins et attentes. Je vous explique ensuite ma méthode et nous procédons au soin adapté à votre situation. Chaque séance est unique et personnalisée.",
          },
          {
            question: "Les soins à distance sont-ils aussi efficaces ?",
            answer:
              "Absolument ! L'énergie n'a pas de frontières. Mes clients à distance obtiennent les mêmes résultats qu'en présentiel. La connexion énergétique se fait naturellement, peu importe la distance physique.",
          },
          {
            question: "Combien de séances sont nécessaires ?",
            answer:
              "Cela dépend de votre situation et de vos objectifs. Certaines personnes ressentent des changements dès la première séance, d'autres préfèrent un accompagnement sur plusieurs mois. Nous adaptons ensemble selon vos besoins.",
          },
          {
            question: "Y a-t-il des contre-indications ?",
            answer:
              "Les soins énergétiques sont doux et naturels, sans contre-indications. Ils complètent parfaitement un suivi médical traditionnel sans jamais s'y substituer. Je respecte toujours vos traitements en cours.",
          },
        ]}
        description="Les réponses aux questions que vous vous posez"
      />

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-gold/10 to-sand/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 md:p-12 shadow-xl border border-sand/30">
            <Heart className="w-12 h-12 text-gold mx-auto mb-6" />

            <h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
              Prêt(e) à écrire votre propre témoignage ?
            </h2>

            <p className="font-doulos text-lg text-olive/80 mb-8 max-w-2xl mx-auto">
              Rejoignez les centaines de personnes qui ont déjà transformé leur vie. Votre parcours vers le mieux-être
              commence par un simple premier pas.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                asChild
                size="lg"
                className="bg-gold hover:bg-gold/90 text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group"
              >
                <Link href="/reservation" className="flex items-center">
                  Prendre rendez-vous
                  <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </Button>

              <Button
                asChild
                variant="outline"
                size="lg"
                className="border-2 border-gold text-gold hover:bg-gold hover:text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-lg"
              >
                <Link href="/services">Découvrir les services</Link>
              </Button>
            </div>

            <p className="font-doulos text-sm text-olive/60 mt-6">
              Premier échange téléphonique gratuit • Accompagnement personnalisé • Résultats durables
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
