import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { ArrowRight, Calendar, Clock, Heart, MapPin, Sparkles, Star, Users } from "lucide-react";
import Link from "next/link";

export default function Events() {
	const upcomingEvents = [
		{
			id: 1,
			title: "Atelier Harmonisation des Chakras",
			date: "15 Mars 2024",
			time: "14h00 - 17h00",
			location: "Cabinet Laurence Gémin",
			participants: "8 personnes max",
			price: "65€",
			description:
				"Découvrez les 7 chakras principaux et apprenez des techniques simples pour maintenir votre équilibre énergétique au quotidien.",
			highlights: [
				"Théorie et pratique des chakras",
				"Méditations guidées personnalisées",
				"Techniques d'auto-harmonisation",
				"Support audio offert",
			],
			status: "Disponible",
		},
		{
			id: 2,
			title: "Cercle de Femmes - Nouvelle Lune",
			date: "28 Mars 2024",
			time: "19h00 - 21h30",
			location: "En ligne (Zoom)",
			participants: "12 femmes max",
			price: "35€",
			description:
				"Un moment privilégié entre femmes pour se reconnecter à sa féminité sacrée et poser ses intentions pour le nouveau cycle lunaire.",
			highlights: [
				"Rituel de nouvelle lune",
				"Partage en cercle bienveillant",
				"Méditation collective",
				"Guidance personnalisée",
			],
			status: "Dernières places",
		},
		{
			id: 3,
			title: "Stage Libération Émotionnelle",
			date: "12-13 Avril 2024",
			time: "9h00 - 17h00 (2 jours)",
			location: "Centre de bien-être Harmonia",
			participants: "10 personnes max",
			price: "180€",
			description:
				"Week-end intensif pour apprendre à identifier et libérer les émotions bloquées qui limitent votre épanouissement.",
			highlights: [
				"Techniques EFT avancées",
				"Travail sur les mémoires cellulaires",
				"Pratiques en binômes",
				"Manuel de techniques offert",
			],
			status: "Disponible",
		},
		{
			id: 4,
			title: "Conférence : Les Soins Énergétiques",
			date: "25 Avril 2024",
			time: "20h00 - 21h30",
			location: "Médiathèque de la ville",
			participants: "50 personnes max",
			price: "Gratuit",
			description:
				"Conférence découverte pour comprendre les principes des soins énergétiques et leurs bienfaits sur la santé globale.",
			highlights: [
				"Introduction aux énergies subtiles",
				"Démonstration pratique",
				"Questions-réponses",
				"Remise de documentation",
			],
			status: "Inscription obligatoire",
		},
	];

	const pastEvents = [
		{
			title: "Atelier Méditation & Énergies",
			date: "Février 2024",
			participants: "12 participants",
			feedback: "Expérience transformatrice selon 100% des participants",
		},
		{
			title: "Cercle de Femmes - Pleine Lune",
			date: "Janvier 2024",
			participants: "15 participantes",
			feedback: "Moment magique de sororité et de partage",
		},
		{
			title: "Stage Développement Personnel",
			date: "Décembre 2023",
			participants: "8 participants",
			feedback: "Outils concrets pour une transformation durable",
		},
	];

	return (
		<div className="pt-20">
			{/* Hero Section */}
			<section className="py-20 bg-gradient-to-br from-cream to-sand/30">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<div className="inline-flex items-center px-4 py-2 rounded-full bg-gold/10 border border-gold/20 mb-6">
						<Calendar className="w-4 h-4 text-gold mr-2" />
						<span className="text-base font-crimson text-gold font-medium">Événements</span>
					</div>

					<h1 className="font-new-york text-4xl md:text-5xl font-bold text-olive mb-6">
						Ateliers & Événements
					</h1>

					<p className="font-doulos text-xl text-olive/80 leading-relaxed mb-8 max-w-2xl mx-auto">
						Participez à mes ateliers collectifs pour approfondir votre développement personnel et partager
						des moments privilégiés avec d'autres personnes en quête de bien-être.
					</p>

					<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
						<Button
							asChild
							size="lg"
							className="bg-gold hover:bg-gold-dark text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group text-lg"
						>
							<Link href="/reservation" className="flex items-center">
								S'inscrire à un événement
								<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
							</Link>
						</Button>

						<Button
							asChild
							variant="outline"
							size="lg"
							className="border-2 border-blue text-blue bg-cream hover:bg-blue hover:text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg"
						>
							<Link href="/services">Séances individuelles</Link>
						</Button>
					</div>
				</div>
			</section>

			{/* Upcoming Events */}
			<section className="py-20 bg-white/80">
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Prochains événements
						</h2>
						<p className="font-doulos text-xl text-olive/80">Découvrez les ateliers et stages à venir</p>
					</div>

					<div className="grid lg:grid-cols-2 gap-8">
						{upcomingEvents.map((event) => (
							<Card
								key={event.id}
								className="bg-gradient-to-br from-cream/50 to-sand/20 border-sand/30 hover:shadow-xl transition-all duration-300 hover:-translate-y-2"
							>
								<CardHeader>
									<div className="flex items-start justify-between mb-4">
										<div className="flex-1">
											<CardTitle className="font-new-york text-xl text-olive mb-2">
												{event.title}
											</CardTitle>
											<div className="space-y-2">
												<div className="flex items-center text-olive/70">
													<Calendar className="w-4 h-4 mr-2" />
													<span className="font-doulos text-base">{event.date}</span>
												</div>
												<div className="flex items-center text-olive/70">
													<Clock className="w-4 h-4 mr-2" />
													<span className="font-doulos text-base">{event.time}</span>
												</div>
												<div className="flex items-center text-olive/70">
													<MapPin className="w-4 h-4 mr-2" />
													<span className="font-doulos text-base">{event.location}</span>
												</div>
												<div className="flex items-center text-olive/70">
													<Users className="w-4 h-4 mr-2" />
													<span className="font-doulos text-base">{event.participants}</span>
												</div>
											</div>
										</div>
										<div className="text-right">
											<div className="text-2xl font-bold text-gold font-new-york mb-1">
												{event.price}
											</div>
											<span
												className={`inline-flex items-center px-2 py-1 rounded-full text-sm font-medium ${
													event.status === "Disponible"
														? "bg-green-100 text-green-800"
														: event.status === "Dernières places"
														? "bg-orange-100 text-orange-800"
														: "bg-blue-100 text-blue-800"
												}`}
											>
												{event.status}
											</span>
										</div>
									</div>
								</CardHeader>

								<CardContent>
									<p className="font-doulos text-olive/80 leading-relaxed mb-6">
										{event.description}
									</p>

									<div className="mb-6">
										<h4 className="font-new-york text-base font-semibold text-olive mb-3">
											Au programme :
										</h4>
										<ul className="space-y-2">
											{event.highlights.map((highlight, index) => (
												<li key={index} className="flex items-start text-base text-olive/80">
													<Star className="w-4 h-4 text-gold mt-0.5 mr-2 flex-shrink-0" />
													<span className="font-doulos">{highlight}</span>
												</li>
											))}
										</ul>
									</div>

									<Button
										asChild
										className="w-full bg-gold hover:bg-gold-dark text-white font-sans font-bold py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg"
									>
										<Link href="/reservation" className="flex items-center justify-center">
											S'inscrire
											<ArrowRight className="ml-3 w-5 h-5" />
										</Link>
									</Button>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Benefits Section */}
			<section className="py-20 bg-gradient-to-br from-olive/5 to-gold/5">
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Pourquoi participer à mes événements ?
						</h2>
						<p className="font-doulos text-xl text-olive/80">
							Les avantages uniques de l'expérience collective
						</p>
					</div>

					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
						{[
							{
								icon: <Heart className="w-8 h-8" />,
								title: "Énergie collective",
								description:
									"L'énergie du groupe amplifie les bienfaits et crée une synergie puissante pour la transformation.",
							},
							{
								icon: <Users className="w-8 h-8" />,
								title: "Partage bienveillant",
								description:
									"Rencontrez des personnes partageant les mêmes aspirations dans un cadre sécurisé et respectueux.",
							},
							{
								icon: <Sparkles className="w-8 h-8" />,
								title: "Apprentissage approfondi",
								description:
									"Apprenez des techniques que vous pourrez pratiquer chez vous pour maintenir votre équilibre.",
							},
							{
								icon: <Star className="w-8 h-8" />,
								title: "Tarifs avantageux",
								description:
									"Bénéficiez d'un accompagnement de qualité à un tarif plus accessible qu'en séance individuelle.",
							},
							{
								icon: <Calendar className="w-8 h-8" />,
								title: "Suivi personnalisé",
								description:
									"Même en groupe, chaque participant reçoit une attention et des conseils personnalisés.",
							},
							{
								icon: <MapPin className="w-8 h-8" />,
								title: "Flexibilité",
								description:
									"Événements en présentiel et en ligne pour s'adapter à vos contraintes géographiques.",
							},
						].map((benefit, index) => (
							<Card
								key={index}
								className="bg-white/80 backdrop-blur-sm border-sand/30 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 text-center"
							>
								<CardContent className="p-6">
									<div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gold/10 to-gold/5 rounded-full flex items-center justify-center">
										<div className="text-gold">{benefit.icon}</div>
									</div>
									<h3 className="font-new-york text-lg font-semibold text-olive mb-3">
										{benefit.title}
									</h3>
									<p className="font-doulos text-olive/80 text-base leading-relaxed">
										{benefit.description}
									</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Past Events */}
			<section className="py-20 bg-white/80">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Événements passés
						</h2>
						<p className="font-doulos text-xl text-olive/80">Retour sur nos derniers ateliers</p>
					</div>

					<div className="space-y-6">
						{pastEvents.map((event, index) => (
							<Card key={index} className="bg-gradient-to-br from-cream/50 to-sand/20 border-sand/30">
								<CardContent className="p-6">
									<div className="flex flex-col md:flex-row md:items-center justify-between">
										<div className="flex-1 mb-4 md:mb-0">
											<h3 className="font-new-york text-lg font-semibold text-olive mb-2">
												{event.title}
											</h3>
											<div className="flex items-center text-olive/70 mb-2">
												<Calendar className="w-4 h-4 mr-2" />
												<span className="font-doulos text-base">{event.date}</span>
												<Users className="w-4 h-4 ml-4 mr-2" />
												<span className="font-doulos text-base">{event.participants}</span>
											</div>
											<p className="font-doulos text-olive/80 text-base italic">
												"{event.feedback}"
											</p>
										</div>
										<div className="flex items-center text-gold">
											<Star className="w-5 h-5 fill-current" />
											<Star className="w-5 h-5 fill-current" />
											<Star className="w-5 h-5 fill-current" />
											<Star className="w-5 h-5 fill-current" />
											<Star className="w-5 h-5 fill-current" />
										</div>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Newsletter Section */}
			<section className="py-20 bg-gradient-to-br from-cream to-sand/30">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 md:p-12 shadow-xl border border-sand/30">
						<Calendar className="w-12 h-12 text-gold mx-auto mb-6" />

						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Ne manquez aucun événement
						</h2>

						<p className="font-doulos text-xl text-olive/80 mb-8 max-w-2xl mx-auto">
							Inscrivez-vous à ma newsletter pour être informé(e) en avant-première de tous mes ateliers
							et événements à venir.
						</p>

						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto mb-6">
							<input
								type="email"
								placeholder="Votre adresse email"
								className="flex-1 px-4 py-3 rounded-full border border-sand focus:border-gold focus:outline-none font-doulos"
							/>
							<Button className="bg-gold hover:bg-gold/90 text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-lg whitespace-nowrap">
								S'abonner
							</Button>
						</div>

						<p className="font-doulos text-base text-olive/60">
							Pas de spam, juste les informations essentielles • Désabonnement facile
						</p>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-20 bg-gradient-to-br from-gold/10 to-sand/20">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 md:p-12 shadow-xl border border-sand/30">
						<Sparkles className="w-12 h-12 text-gold mx-auto mb-6" />

						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Prêt(e) à vivre une expérience transformatrice ?
						</h2>

						<p className="font-doulos text-xl text-olive/80 mb-8 max-w-2xl mx-auto">
							Rejoignez-nous lors de nos prochains événements pour découvrir le pouvoir de la
							transformation collective et révéler votre potentiel.
						</p>

						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Button
								asChild
								size="lg"
								className="bg-gold hover:bg-gold-dark text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group text-lg"
							>
								<Link href="/reservation" className="flex items-center">
									S'inscrire maintenant
									<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
								</Link>
							</Button>

							<Button
								asChild
								variant="outline"
								size="lg"
								className="border-2 border-blue text-blue bg-cream hover:bg-blue hover:text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg"
							>
								<Link href="/services">Séances individuelles</Link>
							</Button>
						</div>
					</div>
				</div>
			</section>
		</div>
	);
}
