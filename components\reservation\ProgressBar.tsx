"use client"

import { CheckCircle } from "lucide-react"

interface ProgressBarProps {
  currentStep: number
  totalSteps: number
  steps: string[]
}

export default function ProgressBar({ currentStep, totalSteps, steps }: ProgressBarProps) {
  return (
    <div className="w-full bg-white/80 backdrop-blur-sm border-b border-sand/30 py-6">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const stepNumber = index + 1
            const isCompleted = stepNumber < currentStep
            const isCurrent = stepNumber === currentStep
            const isUpcoming = stepNumber > currentStep

            return (
              <div key={index} className="flex items-center flex-1">
                {/* Step Circle */}
                <div className="flex items-center">
                  <div
                    className={`
                      w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm transition-all duration-300
                      ${
                        isCompleted
                          ? "bg-gold text-white"
                          : isCurrent
                            ? "bg-gold text-white ring-4 ring-gold/20"
                            : "bg-sand/50 text-olive/50"
                      }
                    `}
                  >
                    {isCompleted ? <CheckCircle className="w-5 h-5" /> : stepNumber}
                  </div>
                  <div className="ml-3 hidden sm:block">
                    <p
                      className={`
                        font-doulos text-sm font-medium transition-colors duration-300
                        ${isCurrent ? "text-gold" : isCompleted ? "text-olive" : "text-olive/50"}
                      `}
                    >
                      {step}
                    </p>
                  </div>
                </div>

                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className="flex-1 mx-4">
                    <div
                      className={`
                        h-0.5 transition-colors duration-300
                        ${isCompleted ? "bg-gold" : "bg-sand/30"}
                      `}
                    />
                  </div>
                )}
              </div>
            )
          })}
        </div>

        {/* Mobile Step Indicator */}
        <div className="sm:hidden mt-4 text-center">
          <p className="font-doulos text-sm text-olive/70">
            Étape {currentStep} sur {totalSteps} : {steps[currentStep - 1]}
          </p>
        </div>
      </div>
    </div>
  )
}
