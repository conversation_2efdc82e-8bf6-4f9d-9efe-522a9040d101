"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>ronLeft, <PERSON><PERSON>ron<PERSON><PERSON>, <PERSON>uo<PERSON>, <PERSON> } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";

interface Testimonial {
	id: number;
	name: string;
	service: string;
	content: string;
	rating: number;
	image?: string;
}

const testimonials: Testimonial[] = [
	{
		id: 1,
		name: "<PERSON><PERSON><PERSON>",
		service: "Soin énergétique complet",
		content:
			"Une expérience absolument transformatrice qui a changé ma vie ! Laurence a su identifier mes blocages énergétiques avec une précision remarquable dès notre première rencontre. J'arrivais épuisée, stressée par mon travail et mes relations personnelles. Après plusieurs séances, j'ai retrouvé une sérénité que je n'avais pas connue depuis des années. Son approche bienveillante et son professionnalisme m'ont permis de me libérer de peurs anciennes qui m'empêchaient d'avancer. Aujourd'hui, je me sens alignée, en paix avec moi-même et capable d'affronter les défis avec confiance.",
		rating: 5,
	},
	{
		id: 2,
		name: "Sophie L.",
		service: "Coaching bien-être",
		content:
			"L'accompagnement de Laurence m'a littéralement sauvée d'une période très sombre de ma vie. Après un divorce difficile et une perte d'emploi, j'étais complètement perdue, sans direction ni espoir. Grâce à ses techniques de coaching et à son écoute exceptionnelle, j'ai pu non seulement surmonter cette épreuve mais aussi redéfinir complètement mes priorités et retrouver ma joie de vivre. Elle m'a donné des outils concrets que j'utilise encore aujourd'hui dans mon quotidien. Son soutien m'a permis de me reconstruire plus forte qu'avant. Un véritable cadeau de la vie !",
		rating: 5,
	},
	{
		id: 3,
		name: "Isabelle M.",
		service: "Harmonisation chakras",
		content:
			"Séance absolument incroyable qui restera gravée dans ma mémoire ! J'ai ressenti un apaisement profond et une clarté mentale que je n'avais pas eue depuis très longtemps. Laurence a une capacité extraordinaire à percevoir les déséquilibres énergétiques et à les corriger avec une douceur remarquable. Pendant la séance, j'ai senti des vagues d'énergie parcourir tout mon corps, libérant des tensions que je portais depuis des années. Après la séance, j'ai eu l'impression de renaître, comme si un voile s'était levé. Les conseils qu'elle m'a donnés m'aident encore aujourd'hui à maintenir cet équilibre. Merci Laurence pour ce moment de pure magie !",
		rating: 5,
	},
	{
		id: 4,
		name: "Catherine R.",
		service: "Libération émotionnelle",
		content:
			"Laurence a une capacité extraordinaire à cerner les besoins profonds et à aller directement à l'essentiel sans jugement. Grâce à ses techniques de libération émotionnelle, j'ai pu me défaire de schémas limitants et de blessures émotionnelles qui me suivaient depuis l'enfance. Le travail que nous avons fait ensemble sur plusieurs mois m'a permis de me réconcilier avec mon passé, de pardonner et d'avancer sereinement dans ma vie. J'ai appris à gérer mes émotions différemment et à transformer mes peurs en force. Cette transformation a eu un impact positif sur tous les aspects de ma vie : relations, travail, confiance en moi. Je recommande vivement son accompagnement !",
		rating: 5,
	},
	{
		id: 5,
		name: "Nathalie B.",
		service: "Soin à distance",
		content:
			"J'étais très sceptique au début concernant l'efficacité des soins à distance, mais Laurence m'a complètement convaincue ! Même à des centaines de kilomètres, la connexion énergétique était palpable et l'efficacité était au rendez-vous. J'ai ressenti les bienfaits du soin immédiatement : une chaleur douce qui m'envahissait, des tensions qui se relâchaient, un apaisement profond. Les effets se sont prolongés plusieurs jours après la séance. Son suivi personnalisé par téléphone et email m'a permis d'intégrer pleinement les changements. Une praticienne d'exception avec un don véritable et une humanité touchante. Je n'hésiterai pas à refaire appel à ses services !",
		rating: 5,
	},
];

const TestimonialSlider = () => {
	const [currentIndex, setCurrentIndex] = useState(0);
	const [isAutoPlaying, setIsAutoPlaying] = useState(true);

	useEffect(() => {
		if (!isAutoPlaying) return;

		const interval = setInterval(() => {
			setCurrentIndex((prevIndex) => (prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1));
		}, 5000);

		return () => clearInterval(interval);
	}, [isAutoPlaying]);

	const goToPrevious = () => {
		setIsAutoPlaying(false);
		setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1);
	};

	const goToNext = () => {
		setIsAutoPlaying(false);
		setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1);
	};

	const goToSlide = (index: number) => {
		setIsAutoPlaying(false);
		setCurrentIndex(index);
	};

	const currentTestimonial = testimonials[currentIndex];

	return (
		<div className="relative max-w-4xl mx-auto">
			{/* Main Testimonial */}
			<div
				className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 md:p-12 shadow-2xl border-2 border-blue/20 relative overflow-hidden hover:shadow-3xl transition-all duration-300"
				onMouseEnter={() => setIsAutoPlaying(false)}
				onMouseLeave={() => setIsAutoPlaying(true)}
			>
				{/* Background Pattern */}
				<div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue/10 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

				{/* Quote Icon */}
				<div className="absolute top-6 left-6">
					<Quote className="w-10 h-10 text-gold/30" />
				</div>

				<div className="relative z-10">
					{/* Stars */}
					<div className="flex justify-center mb-6">
						{[...Array(currentTestimonial.rating)].map((_, i) => (
							<Star key={i} className="w-5 h-5 text-gold fill-current" />
						))}
					</div>

					{/* Content */}
					<blockquote className="text-center mb-8">
						<p className="font-dm-serif text-xl md:text-2xl text-olive leading-relaxed italic font-medium">
							"{currentTestimonial.content}"
						</p>
					</blockquote>

					{/* Author */}
					<div className="text-center">
						<div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-gold/20 to-sand/20 flex items-center justify-center">
							<Image
								src="/placeholder.svg?key=m29f5"
								alt={currentTestimonial.name}
								width={64}
								height={64}
								className="rounded-full object-cover"
							/>
						</div>
						<h4 className="font-new-york text-lg font-semibold text-olive mb-1">
							{currentTestimonial.name}
						</h4>
						<p className="font-sans text-base text-olive/60">{currentTestimonial.service}</p>
					</div>
				</div>
			</div>

			{/* Navigation */}
			<div className="flex items-center justify-center mt-8 space-x-4">
				<Button
					variant="outline"
					size="sm"
					onClick={goToPrevious}
					className="rounded-full w-10 h-10 p-0 border-gold/30 hover:bg-gold hover:text-white transition-all duration-300"
				>
					<ChevronLeft className="w-4 h-4" />
				</Button>

				{/* Dots */}
				<div className="flex space-x-2">
					{testimonials.map((_, index) => (
						<button
							key={index}
							onClick={() => goToSlide(index)}
							className={`w-2 h-2 rounded-full transition-all duration-300 ${
								index === currentIndex ? "bg-gold w-8" : "bg-gold/30 hover:bg-gold/50"
							}`}
						/>
					))}
				</div>

				<Button
					variant="outline"
					size="sm"
					onClick={goToNext}
					className="rounded-full w-10 h-10 p-0 border-gold/30 hover:bg-gold hover:text-white transition-all duration-300"
				>
					<ChevronRight className="w-4 h-4" />
				</Button>
			</div>

			{/* Progress Bar */}
			<div className="mt-4 w-full bg-sand/30 rounded-full h-1 overflow-hidden">
				<div
					className="h-full bg-gold transition-all duration-300 ease-out"
					style={{ width: `${((currentIndex + 1) / testimonials.length) * 100}%` }}
				/>
			</div>
		</div>
	);
};

export default TestimonialSlider;
