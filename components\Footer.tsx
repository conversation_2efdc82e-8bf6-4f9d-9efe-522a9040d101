import { Facebook, Heart, Instagram, Mail, Star } from "lucide-react";
import Link from "next/link";

const Footer = () => {
	return (
		<footer className="bg-gradient-to-br from-olive to-blue-variant text-cream">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
				<div className="grid md:grid-cols-4 gap-10">
					{/* Brand */}
					<div className="md:col-span-2">
						<div className="flex items-center space-x-3 mb-6">
							<div className="w-12 h-12 rounded-full bg-gold flex items-center justify-center shadow-lg">
								<Star className="w-6 h-6 text-white" />
							</div>
							<div>
								<h3 className="font-early-sunday text-3xl font-bold text-gold">Laurence <PERSON></h3>
								<p className="text-lg font-dm-serif italic text-cream/90">
									Coach en soins énergétiques
								</p>
							</div>
						</div>
						<p className="font-sans text-cream/90 mb-8 max-w-md leading-relaxed text-xl font-medium">
							Accompagnement personnalisé pour retrouver votre équilibre intérieur et révéler votre
							potentiel authentique à travers les soins énergétiques et le coaching bien-être.
						</p>
						<div className="flex space-x-4">
							<Link
								href="#"
								className="w-12 h-12 bg-cream/15 hover:bg-gold rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg"
							>
								<Instagram className="w-6 h-6" />
							</Link>
							<Link
								href="#"
								className="w-12 h-12 bg-cream/15 hover:bg-gold rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg"
							>
								<Facebook className="w-6 h-6" />
							</Link>
							<Link
								href="mailto:<EMAIL>"
								className="w-12 h-12 bg-cream/15 hover:bg-gold rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg"
							>
								<Mail className="w-6 h-6" />
							</Link>
						</div>
					</div>

					{/* Navigation */}
					<div>
						<h4 className="font-new-york text-lg font-semibold text-gold mb-4">Navigation</h4>
						<ul className="space-y-2">
							{[
								{ name: "Accueil", href: "/" },
								{ name: "À propos", href: "/about" },
								{ name: "Services", href: "/services" },
								{ name: "Témoignages", href: "/testimonials" },
								{ name: "Événements", href: "/events" },
								{ name: "Contact", href: "/contact" },
							].map((item) => (
								<li key={item.name}>
									<Link
										href={item.href}
										className="font-doulos text-cream/80 hover:text-gold transition-colors duration-200"
									>
										{item.name}
									</Link>
								</li>
							))}
						</ul>
					</div>

					{/* Services */}
					<div>
						<h4 className="font-new-york text-lg font-semibold text-gold mb-4">Services</h4>
						<ul className="space-y-2">
							{[
								"Soins énergétiques",
								"Coaching bien-être",
								"Harmonisation chakras",
								"Libération émotionnelle",
								"Soins à distance",
							].map((service) => (
								<li key={service}>
									<span className="font-sans text-cream/80 text-base">{service}</span>
								</li>
							))}
						</ul>
					</div>
				</div>

				{/* Bottom Bar */}
				<div className="border-t border-cream/20 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
					<div className="flex items-center space-x-2 text-cream/60 text-base font-sans mb-4 md:mb-0">
						<span>© {new Date().getFullYear()} Laurence Gémin</span>
						<span>•</span>
						<span>Fait avec</span>
						<Heart className="w-4 h-4 text-gold fill-current" />
						<span>pour votre bien-être</span>
					</div>

					<div className="flex space-x-6 text-cream/60 text-base font-sans">
						<Link href="/mentions-legales" className="hover:text-gold transition-colors duration-200">
							Mentions légales
						</Link>
						<Link
							href="/politique-confidentialite"
							className="hover:text-gold transition-colors duration-200"
						>
							Confidentialité
						</Link>
					</div>
				</div>
			</div>
		</footer>
	);
};

export default Footer;
