"use client";

import type React from "react";

import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar, Clock, Phone } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import DateSelector from "./DateSelector";
import TimeSlotPicker from "./TimeSlotPicker";

export default function FirstEncounterForm() {
	const router = useRouter();
	const [formData, setFormData] = useState({
		firstName: "",
		lastName: "",
		email: "",
		phone: "",
		selectedDate: "",
		selectedTime: "",
	});
	const [isSubmitting, setIsSubmitting] = useState(false);

	const handleInputChange = (field: string, value: string) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsSubmitting(true);

		// TODO: connect backend here
		// Simulate API call
		setTimeout(() => {
			// Redirect to confirmation with data
			const params = new URLSearchParams({
				service: "Entretien découverte",
				firstName: formData.firstName,
				lastName: formData.lastName,
				email: formData.email,
				phone: formData.phone,
				date: formData.selectedDate,
				time: formData.selectedTime,
				duration: "15-20 min",
				price: "0",
				type: "phone",
			});

			router.push(`/reservation/confirmation?${params.toString()}`);
		}, 2000);
	};

	const isFormValid = () => {
		return (
			formData.firstName &&
			formData.lastName &&
			formData.email &&
			formData.phone &&
			formData.selectedDate &&
			formData.selectedTime
		);
	};

	return (
		<Card className="bg-white/80 backdrop-blur-sm border-sand/30 shadow-lg">
			<CardHeader>
				<CardTitle className="font-new-york text-2xl text-olive text-center">Planifier votre appel</CardTitle>
				<p className="font-doulos text-olive/70 text-center">
					Choisissez un créneau pour votre entretien découverte gratuit
				</p>
			</CardHeader>

			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-6">
					{/* Personal Info */}
					<div className="grid md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="firstName" className="font-doulos text-olive">
								Prénom *
							</Label>
							<Input
								id="firstName"
								value={formData.firstName}
								onChange={(e) => handleInputChange("firstName", e.target.value)}
								required
								className="border-sand focus:border-gold"
								placeholder="Votre prénom"
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="lastName" className="font-doulos text-olive">
								Nom *
							</Label>
							<Input
								id="lastName"
								value={formData.lastName}
								onChange={(e) => handleInputChange("lastName", e.target.value)}
								required
								className="border-sand focus:border-gold"
								placeholder="Votre nom"
							/>
						</div>
					</div>

					<div className="grid md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="email" className="font-doulos text-olive">
								Email *
							</Label>
							<Input
								id="email"
								type="email"
								value={formData.email}
								onChange={(e) => handleInputChange("email", e.target.value)}
								required
								className="border-sand focus:border-gold"
								placeholder="<EMAIL>"
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="phone" className="font-doulos text-olive">
								Téléphone *
							</Label>
							<Input
								id="phone"
								type="tel"
								value={formData.phone}
								onChange={(e) => handleInputChange("phone", e.target.value)}
								required
								className="border-sand focus:border-gold"
								placeholder="06 12 34 56 78"
							/>
						</div>
					</div>

					{/* Date Selection */}
					<div className="space-y-3">
						<Label className="font-doulos text-olive flex items-center">
							<Calendar className="w-4 h-4 mr-2" />
							Choisissez une date *
						</Label>
						<DateSelector
							selectedDate={formData.selectedDate}
							onDateSelect={(date) => handleInputChange("selectedDate", date)}
						/>
					</div>

					{/* Time Selection */}
					{formData.selectedDate && (
						<div className="space-y-3">
							<Label className="font-doulos text-olive flex items-center">
								<Clock className="w-4 h-4 mr-2" />
								Choisissez un créneau *
							</Label>
							<TimeSlotPicker
								selectedDate={formData.selectedDate}
								selectedTime={formData.selectedTime}
								onTimeSelect={(time) => handleInputChange("selectedTime", time)}
							/>
						</div>
					)}

					{/* Info Box */}
					<div className="bg-gradient-to-br from-gold/5 to-cream/30 rounded-lg p-4">
						<div className="flex items-start space-x-3">
							<Phone className="w-5 h-5 text-gold mt-1 flex-shrink-0" />
							<div>
								<h4 className="font-doulos font-medium text-olive mb-1">Modalité de l'entretien</h4>
								<p className="font-doulos text-olive/80 text-sm">
									Je vous appellerai au numéro indiqué à l'heure convenue. Assurez-vous d'être dans un
									endroit calme où vous pourrez parler librement.
								</p>
							</div>
						</div>
					</div>

					{/* Submit Button */}
					<Button
						type="submit"
						disabled={!isFormValid() || isSubmitting}
						className="w-full bg-gold hover:bg-gold/90 text-white font-doulos font-medium py-4 rounded-full transition-all duration-300 hover:shadow-lg disabled:opacity-50 min-h-[48px]"
					>
						{isSubmitting ? (
							<div className="flex items-center justify-center">
								<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
								Planification en cours...
							</div>
						) : (
							<div className="flex items-center justify-center">
								<Phone className="w-4 h-4 mr-2" />
								Planifier mon appel
							</div>
						)}
					</Button>
				</form>
			</CardContent>
		</Card>
	);
}
